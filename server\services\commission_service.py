"""
佣金计算服务
实现新的佣金逻辑：邀请人的收益 = 任务数 * 基本价格 + 被邀请人提交的任务数 * 价格 * 比例
"""
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import Dict, List, Optional
from decimal import Decimal

from ..models import User, Task, Platform, Settlement
from ..models.task import TaskStatus
from ..models.settlement import SettlementStatus


class CommissionService:
    """佣金计算服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def calculate_user_commission(self, user_id: int) -> Dict:
        """
        计算用户的总佣金收益
        
        Args:
            user_id: 用户ID
            
        Returns:
            dict: {
                'total_earnings': Decimal,  # 总收益
                'task_earnings': Decimal,   # 自己任务收益
                'referral_earnings': Decimal,  # 邀请收益
                'settled_amount': Decimal,  # 已结算金额
                'available_balance': Decimal,  # 可用余额
                'task_count': int,  # 自己完成任务数
                'referral_task_count': int,  # 被邀请人完成任务数
                'platform_details': List[Dict]  # 各平台详情
            }
        """
        # 决策理由：按平台分别计算，因为不同平台价格和比例不同
        platform_details = []
        total_task_earnings = Decimal('0')
        total_referral_earnings = Decimal('0')
        total_task_count = 0
        total_referral_task_count = 0
        
        # 获取所有活跃平台
        platforms = self.db.query(Platform).filter(
            Platform.status == 'active'
        ).all()
        
        for platform in platforms:
            platform_data = self._calculate_platform_commission(user_id, platform)
            platform_details.append(platform_data)
            
            total_task_earnings += platform_data['task_earnings']
            total_referral_earnings += platform_data['referral_earnings']
            total_task_count += platform_data['task_count']
            total_referral_task_count += platform_data['referral_task_count']
        
        # 计算已结算金额
        settled_amount = self._get_settled_amount(user_id)
        
        # 计算总收益和可用余额
        total_earnings = total_task_earnings + total_referral_earnings
        available_balance = total_earnings - settled_amount
        
        return {
            'total_earnings': total_earnings,
            'task_earnings': total_task_earnings,
            'referral_earnings': total_referral_earnings,
            'settled_amount': settled_amount,
            'available_balance': available_balance,
            'task_count': total_task_count,
            'referral_task_count': total_referral_task_count,
            'platform_details': platform_details
        }
    
    def _calculate_platform_commission(self, user_id: int, platform: Platform) -> Dict:
        """
        计算用户在特定平台的佣金收益
        
        Args:
            user_id: 用户ID
            platform: 平台对象
            
        Returns:
            dict: 平台佣金详情
        """
        # 统计用户自己完成的任务数
        task_count = self.db.query(func.count(Task.id)).filter(
            Task.assigned_to == user_id,
            Task.platform_id == platform.id,
            Task.status == TaskStatus.completed
        ).scalar() or 0
        
        # 统计被邀请人完成的任务数
        referral_task_count = self.db.query(func.count(Task.id)).join(
            User, Task.assigned_to == User.id
        ).filter(
            User.referrer_id == user_id,
            Task.platform_id == platform.id,
            Task.status == TaskStatus.completed
        ).scalar() or 0
        
        # 计算收益
        # 决策理由：按照新公式计算 - 任务数 * 基本价格 + 被邀请人任务数 * 价格 * 比例
        task_earnings = Decimal(str(task_count)) * platform.base_price
        referral_earnings = (Decimal(str(referral_task_count)) * 
                           platform.base_price * 
                           platform.commission_rate)
        
        return {
            'platform_id': platform.id,
            'platform_code': platform.code,
            'platform_name': platform.name,
            'base_price': platform.base_price,
            'commission_rate': platform.commission_rate,
            'task_count': task_count,
            'referral_task_count': referral_task_count,
            'task_earnings': task_earnings,
            'referral_earnings': referral_earnings,
            'total_earnings': task_earnings + referral_earnings
        }
    
    def _get_settled_amount(self, user_id: int) -> Decimal:
        """
        获取用户已结算金额
        
        Args:
            user_id: 用户ID
            
        Returns:
            Decimal: 已结算金额
        """
        settled_amount = self.db.query(func.sum(Settlement.amount)).filter(
            Settlement.user_id == user_id,
            Settlement.status == SettlementStatus.completed
        ).scalar()
        
        return Decimal(str(settled_amount)) if settled_amount else Decimal('0')
    
    def get_referral_users_commission(self, user_id: int) -> List[Dict]:
        """
        获取被邀请用户的佣金贡献详情
        
        Args:
            user_id: 邀请人用户ID
            
        Returns:
            List[Dict]: 被邀请用户列表及其贡献
        """
        # 获取所有被邀请的用户
        referral_users = self.db.query(User).filter(
            User.referrer_id == user_id,
            User.status == 'active'
        ).all()
        
        result = []
        for referral_user in referral_users:
            # 统计该用户完成的任务数（按平台分组）
            platform_tasks = self.db.query(
                Platform.id,
                Platform.code,
                Platform.name,
                Platform.base_price,
                Platform.commission_rate,
                func.count(Task.id).label('task_count')
            ).join(
                Task, Task.platform_id == Platform.id
            ).filter(
                Task.assigned_to == referral_user.id,
                Task.status == TaskStatus.completed
            ).group_by(Platform.id).all()
            
            total_contribution = Decimal('0')
            platform_details = []
            
            for platform_task in platform_tasks:
                contribution = (Decimal(str(platform_task.task_count)) * 
                              platform_task.base_price * 
                              platform_task.commission_rate)
                total_contribution += contribution
                
                platform_details.append({
                    'platform_code': platform_task.code,
                    'platform_name': platform_task.name,
                    'task_count': platform_task.task_count,
                    'contribution': contribution
                })
            
            result.append({
                'user_id': referral_user.id,
                'username': referral_user.username,
                'total_contribution': total_contribution,
                'platform_details': platform_details,
                'created_at': referral_user.created_at
            })
        
        return result
