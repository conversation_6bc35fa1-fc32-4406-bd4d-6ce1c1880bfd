"""
数据库初始化和管理
"""
from sqlalchemy import text
from .models.base import engine, SessionLocal, Base
from .models import *  # 导入所有模型以确保表被创建
import logging

logger = logging.getLogger(__name__)


def init_db():
    """初始化数据库，创建所有表和初始数据"""
    try:
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        logger.info("数据库表创建成功")
        
        # 插入初始数据
        insert_initial_data()
        logger.info("初始数据插入成功")
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise


def insert_initial_data():
    """插入初始数据"""
    db = SessionLocal()
    try:
        # 检查是否已有数据，避免重复插入
        existing_platforms = db.query(Platform).count()
        if existing_platforms > 0:
            logger.info("初始数据已存在，跳过插入")
            return
        
        # 插入平台数据
        platforms_data = [
            {
                'code': 'tb',
                'name': '淘宝',
                'base_price': 1.00,
                'commission_rate': 0.10,
                'status': PlatformStatus.active
            },
            {
                'code': 'jd', 
                'name': '京东',
                'base_price': 0.80,
                'commission_rate': 0.08,
                'status': PlatformStatus.active
            },
            {
                'code': 'pdd',
                'name': '拼多多', 
                'base_price': 0.60,
                'commission_rate': 0.06,
                'status': PlatformStatus.active
            }
        ]
        
        for platform_data in platforms_data:
            platform = Platform(**platform_data)
            db.add(platform)
        
        # 插入系统配置
        system_settings = [
            {
                'key': 'min_settlement_amount',
                'value': '10.00',
                'description': '最低结算金额'
            },
            {
                'key': 'task_timeout_minutes',
                'value': '30',
                'description': '任务超时时间(分钟)'
            },
            {
                'key': 'max_active_tasks_per_user',
                'value': '3',
                'description': '用户最大同时任务数'
            }
        ]
        
        for setting_data in system_settings:
            setting = SystemSetting(**setting_data)
            db.add(setting)
        
        # 创建默认管理员账号（密码需要实际加密）
        # 注意：这里使用明文密码仅用于演示，生产环境必须使用加密密码
        admin = Admin(
            username='admin',
            password_hash='$2b$12$placeholder_hash',  # 实际应用中需要使用bcrypt加密
            role=AdminRole.super_admin
        )
        db.add(admin)
        
        db.commit()
        logger.info("初始数据插入完成")
        
    except Exception as e:
        db.rollback()
        logger.error(f"初始数据插入失败: {e}")
        raise
    finally:
        db.close()


def reset_db():
    """重置数据库（删除所有表并重新创建）"""
    try:
        # 删除所有表
        Base.metadata.drop_all(bind=engine)
        logger.info("数据库表删除成功")
        
        # 重新初始化
        init_db()
        logger.info("数据库重置完成")
        
    except Exception as e:
        logger.error(f"数据库重置失败: {e}")
        raise


def get_db_session():
    """获取数据库会话（用于依赖注入）"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


if __name__ == "__main__":
    # 直接运行此文件时初始化数据库
    logging.basicConfig(level=logging.INFO)
    init_db()
    print("数据库初始化完成！")
