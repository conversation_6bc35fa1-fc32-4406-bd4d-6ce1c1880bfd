"""
任务相关API接口
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Optional, List
from datetime import datetime
import logging

from ..database import get_db_session
from ..models import Task, Platform, User, TaskResult
from ..models.task import TaskStatus
from ..services import CommissionService
from pydantic import BaseModel

logger = logging.getLogger(__name__)
router = APIRouter()


# Pydantic模型定义
class TaskPullRequest(BaseModel):
    """拉取任务请求"""
    platform_code: str
    user_id: int
    task_type: Optional[str] = "BasicInfo"


class TaskSubmitRequest(BaseModel):
    """提交任务结果请求"""
    task_id: int
    user_id: int
    result_data: dict
    success: bool
    error_message: Optional[str] = None


class TaskResponse(BaseModel):
    """任务响应"""
    id: int
    item_id: str
    platform_code: str
    task_type: str
    assigned_at: Optional[datetime]


class TaskSubmitResponse(BaseModel):
    """任务提交响应"""
    success: bool
    message: str
    commission_earned: Optional[float] = None


@router.post("/pull", response_model=Optional[TaskResponse])
async def pull_task(
    request: TaskPullRequest,
    db: Session = Depends(get_db_session)
):
    """
    拉取任务接口
    
    Args:
        request: 拉取任务请求
        db: 数据库会话
        
    Returns:
        TaskResponse: 分配的任务信息，如果没有可用任务返回None
    """
    try:
        # 验证用户存在
        user = db.query(User).filter(User.id == request.user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 验证平台存在
        platform = db.query(Platform).filter(
            Platform.code == request.platform_code,
            Platform.status == 'active'
        ).first()
        if not platform:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="平台不存在或已禁用"
            )
        
        # 检查用户当前进行中的任务数量
        active_tasks_count = db.query(Task).filter(
            Task.assigned_to == request.user_id,
            Task.status.in_([TaskStatus.assigned, TaskStatus.in_progress])
        ).count()
        
        # 决策理由：限制用户同时进行的任务数，避免任务积压
        max_active_tasks = 3  # 可以从系统配置中读取
        if active_tasks_count >= max_active_tasks:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"用户当前有{active_tasks_count}个进行中的任务，超过最大限制{max_active_tasks}"
            )
        
        # 查找可用任务（优先分配最早创建的任务）
        available_task = db.query(Task).filter(
            Task.platform_id == platform.id,
            Task.task_type == request.task_type,
            Task.status == TaskStatus.pending
        ).order_by(Task.created_at).first()
        
        if not available_task:
            return None  # 没有可用任务
        
        # 分配任务给用户
        available_task.assigned_to = request.user_id
        available_task.status = TaskStatus.assigned
        available_task.assigned_at = datetime.now()
        
        db.commit()
        
        logger.info(f"任务 {available_task.id} 已分配给用户 {request.user_id}")
        
        return TaskResponse(
            id=available_task.id,
            item_id=available_task.item_id,
            platform_code=platform.code,
            task_type=available_task.task_type,
            assigned_at=available_task.assigned_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"拉取任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="拉取任务失败"
        )


@router.post("/submit", response_model=TaskSubmitResponse)
async def submit_task(
    request: TaskSubmitRequest,
    db: Session = Depends(get_db_session)
):
    """
    提交任务结果接口
    
    Args:
        request: 提交任务请求
        db: 数据库会话
        
    Returns:
        TaskSubmitResponse: 提交结果
    """
    try:
        # 验证任务存在且属于该用户
        task = db.query(Task).filter(
            Task.id == request.task_id,
            Task.assigned_to == request.user_id
        ).first()
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在或不属于该用户"
            )
        
        # 检查任务状态
        if task.status not in [TaskStatus.assigned, TaskStatus.in_progress]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"任务状态错误，当前状态: {task.status.value}"
            )
        
        # 创建任务结果记录
        task_result = TaskResult(
            task_id=request.task_id,
            user_id=request.user_id,
            result_data=request.result_data,
            success=request.success,
            error_message=request.error_message
        )
        db.add(task_result)
        
        # 更新任务状态
        if request.success:
            task.status = TaskStatus.completed
            task.completed_at = datetime.now()
            status_message = "任务完成成功"
        else:
            task.status = TaskStatus.failed
            status_message = "任务执行失败"
        
        db.commit()
        
        # 如果任务成功完成，计算佣金收益（仅用于显示，不存储）
        commission_earned = None
        if request.success:
            commission_service = CommissionService(db)
            commission_data = commission_service.calculate_user_commission(request.user_id)
            
            # 决策理由：返回本次任务可能带来的收益变化，帮助用户了解收益情况
            # 这里简化处理，返回用户在该平台的基础收益
            platform = db.query(Platform).filter(Platform.id == task.platform_id).first()
            if platform:
                commission_earned = float(platform.base_price)
        
        logger.info(f"用户 {request.user_id} 提交任务 {request.task_id} 结果: {request.success}")
        
        return TaskSubmitResponse(
            success=True,
            message=status_message,
            commission_earned=commission_earned
        )
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"提交任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="提交任务失败"
        )


@router.get("/user/{user_id}/active")
async def get_user_active_tasks(
    user_id: int,
    db: Session = Depends(get_db_session)
):
    """
    获取用户当前进行中的任务
    
    Args:
        user_id: 用户ID
        db: 数据库会话
        
    Returns:
        List[TaskResponse]: 用户的活跃任务列表
    """
    try:
        # 验证用户存在
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 查询用户的活跃任务
        active_tasks = db.query(Task).join(Platform).filter(
            Task.assigned_to == user_id,
            Task.status.in_([TaskStatus.assigned, TaskStatus.in_progress])
        ).all()
        
        result = []
        for task in active_tasks:
            result.append(TaskResponse(
                id=task.id,
                item_id=task.item_id,
                platform_code=task.platform.code,
                task_type=task.task_type,
                assigned_at=task.assigned_at
            ))
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户活跃任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户活跃任务失败"
        )
